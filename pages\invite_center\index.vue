<template>
    <tm-app>
        <view class="main">
            <customNavigationBar :label="NavigationBarTitle || '推荐中心'" :scrollTop="scrollTop" :showBack="true" />
            <view class="swipe">
                <tm-carousel autoplay :round="0" :width="750" :height="624" model="dot" imgmodel="widthFix"
                    color="#FCD9DB" :list="bannerList" rangKey="src" @click="i=>goLink(bannerList[i].link)"></tm-carousel>
            </view>
            <view class="container">
                <view class="title">
                    <text class="title-bar"></text>
                    推荐中心
                </view>

                <view class="select">
                    <view class="btn" :class="{active: type === 'customer'}" @click="type = 'customer'">推荐客户</view>
                    <view class="btn" :class="{active: type === 'aunt'}" @click="type = 'aunt'">推荐姐妹</view>
                </view>

                <!-- 申请表单 -->
                <view class="form-wrapper">
                    <view class="form-item" @click="openWorkTypePicker">
                        <text class="text reqired">服务类型</text>
                        <input class="input" type="text" v-model="workType" placeholder="请选择服务类型"
                            placeholder-style="color: #D3D2D2" disabled />
                    </view>

                    <!-- <view class="form-item" @click="openAreaPicker">
                        <text class="text">服务区域</text>
                        <input class="input" type="text" v-model="area" placeholder="请选择服务区域"
                            placeholder-style="color: #D3D2D2" disabled />
                    </view> -->

                    <view class="form-item">
                        <text class="text reqired" v-if="type === 'customer'">客户姓名</text>
                        <text class="text reqired" v-if="type === 'aunt'">姐妹姓名</text>
                        <input class="input" type="text" v-model="username" placeholder="请输入姓名" placeholder-style="color: #D3D2D2" />
                    </view>

                    <view class="form-item">
                        <text class="text reqired" v-if="type === 'customer'">客户手机</text>
                        <text class="text reqired" v-if="type === 'aunt'">姐妹手机</text>
                        <input class="input" type="number" v-model="phone" placeholder="请输入手机号" placeholder-style="color: #D3D2D2" />
                    </view>

                    <view class="form-item">
                        <text class="text">上传图片</text>
                        <view class="right">
                            <tm-upload 
                                v-model="list" 
                                :width="list.length?420:210" 
                                :rows="list.length?2:1" 
                                :imageHeight="200" 
                                :url="uploadUrl" 
                                formName="photo"
                                :maxSize="15*1024*1024"
                                :onSuccessAfter="onSuccess"
                                :onRemove="e=>onRemove(e,'18')"
                                :chooesefileAfter="chooesefileAfter"
                                :formData="{
                                    token,
                                    type:'18'
                                }">
                            </tm-upload>
                        </view>
                    </view>
                </view>
                <view class="submit-btn" @click="submit">立即提交</view>
            </view>

            <!-- 服务类型选择器 -->
            <tm-picker
                :show="showPicker"
                :columns="serviceList"
                @confirm="onConfirmWorkType"
                @cancel="showPicker = false"
                v-model:model-str="workType"
                :immediateChange="true"
                color="#F31630"
            />

            <!-- 地区选择器 -->
            <tm-picker
                :show="showAreaPicker"
                :columns="areaList"
                @confirm="onAreaConfirm"
                @cancel="showAreaPicker = false"
                v-model:model-str="area"
                :immediateChange="true"
            />
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
import * as api from '@/api/index.js'
import { goLink } from '@/until/index'
import { useStore } from '@/until/mainpinia'
import { snb } from '@/components/customNavigationBar/snb'
import { share } from '@/tmui/tool/lib/share'
import { useIdCardUpload } from '@/until/useIdCardUpload'
import { parseParams } from '@/until/parseParams'

// 数据定义
interface Banner {
    id: string;
    title: string;
    link: string;
    src: string;
    is_auth: string;
}

interface ServiceItem {
    text: string;
    value: number;
}

interface AreaItem {
    text: string;
    value: number;
}

const store = useStore()
const { NavigationBarTitle } = snb()
const scrollTop = ref(0)

// 使用图片上传hook
const { list, idObj, chooesefileAfter, onSuccess, onRemove } = useIdCardUpload()
const uploadUrl = api.baseUrl + '/Center/uploadPhotoSave'
const token = computed(() => store.token)

// 监听图片ID变化
watch(idObj, val => {
    const temp = Object.keys(val).map(key => val[key])
    picId.value = temp.join(',')
}, {
    deep: true
})

// 页面数据
const bannerList = ref<Banner[]>([])
const serviceList = ref<ServiceItem[]>([])
const areaList = ref<AreaItem[]>([])

// 表单数据
const type = ref('customer')
const workType = ref('')
const area = ref('')
const username = ref('')
const phone = ref('')
const selectedServiceValue = ref(0)
const selectedAreaValue = ref(0)
const picId = ref('')
const hid = ref(0)
const uid = ref(0)

// 选择器控制
const showPicker = ref(false)
const showAreaPicker = ref(false)


// 页面滚动
onPageScroll((e) => {
    scrollTop.value = e.scrollTop
})

// 获取页面数据
const getRecommendOptions = async () => {
    const res = await api.request.ajax({
        url: '/money/recommendOptions',
        type: 'POST',
    })

    if (res.code === 1) {
        bannerList.value = res.data.banner
        // 转换服务列表数据结构
        serviceList.value = res.data.serviceList.map(item => ({
            text: item.name,
            value: item.value
        }))
        // 转换地区列表数据结构
        areaList.value = res.data.areaList.map(item => ({
            text: item.name,
            value: item.value
        }))

        // 设置分享内容
        setShareApp(res.data.shareData)
        setShareTime(res.data.shareData)
    }
}

// 提交申请
const submit = async () => {
    if (!workType.value) {
        uni.showToast({
            title: '请选择服务类型',
            icon: 'none'
        })
        return
    }

    // if (!area.value) {
    //     uni.showToast({
    //         title: '请选择服务区域',
    //         icon: 'none'
    //     })
    //     return
    // }

    if (!username.value) {
        uni.showToast({
            title: '请输入姓名',
            icon: 'none'
        })
        return
    }

    if (!phone.value) {
        uni.showToast({
            title: '请输入手机号',
            icon: 'none'
        })
        return
    }

    const res = await api.request.ajax({
        url: '/money/recommendSave',
        type: 'POST',
        data: {
            type: type.value,
            uname: username.value,
            phone: phone.value,
            service: selectedServiceValue.value,
            hid: hid.value,
            uid: uid.value,
            attachments: picId.value
        }
    })

    if (res.code === 1) {
        uni.showModal({
            title: '提交成功',
            icon: 'success',
            showCancel: false,
            success: () => {
                uni.navigateBack({
                    fail: () => {
                        uni.reLaunch({
                            url: '/pages/index/index'
                        })
                    }
                })
            }
        })
        // 提交成功后的处理逻辑
    } else {
        uni.showToast({
            title: res.msg,
            icon: 'none'
        })
    }
}

// 分享配置
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// 页面加载
onLoad((e) => {
    const params = parseParams(e)
    hid.value = params.hid || hid.value
    uid.value = params.uid || uid.value
    type.value = params.type || 'customer'
    getRecommendOptions()
})

// 打开服务类型选择器
const openWorkTypePicker = () => {
    if (serviceList.value.length === 0) {
        uni.showToast({
            title: '暂无可选服务类型',
            icon: 'none'
        })
        return
    }
    showPicker.value = true
}

// 打开地区选择器
const openAreaPicker = () => {
    if (areaList.value.length === 0) {
        uni.showToast({
            title: '暂无可选服务区域',
            icon: 'none'
        })
        return
    }
    showAreaPicker.value = true
}

// 服务类型选择确认
const onConfirmWorkType = (e: any) => {
    const selectedItem = serviceList.value.find(item => item.text === workType.value)
    if (selectedItem) {
        selectedServiceValue.value = selectedItem.value
    }
    showPicker.value = false
}

// 地区选择确认
const onAreaConfirm = (e: any) => {
    const selectedItem = areaList.value.find(item => item.text === area.value)
    if (selectedItem) {
        selectedAreaValue.value = selectedItem.value
    }
    showAreaPicker.value = false
}
</script>

<style lang="scss" scoped>
.main {
    width: 750rpx;
    height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    background-color: #fff;

    .container {
        margin-top: 240rpx;
        width: 690rpx;
        background: #FFFFFF;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        border-radius: 20rpx 26rpx 26rpx 20rpx;
        padding: 30rpx;
        position: relative;

        .title {
            font-size: 32rpx;
            padding-bottom: 20rpx;
            border-bottom: 1rpx solid #EBEBEB;
            color: #333333;
            font-weight: bold;
            display: flex;
            align-items: center;

            .title-bar {
                width: 8rpx;
                height: 40rpx;
                border-radius: 2rpx;
                background: #FF4B4B;
                margin-right: 20rpx;
            }
        }


        .select {
            margin-top: 60rpx;
            padding-bottom: 60rpx;
            width: 610rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .btn{
                width: 289rpx;
                height: 80rpx;
                background: #FFFFFF;
                box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(187,187,187,0.41);
                border-radius: 26rpx;
                border: 1rpx solid #A1A1A1;
                font-size: 30rpx;
                color: #A6A6A6;
                font-weight: bold;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .active{
                background: linear-gradient(-15deg, #F31630, #FF6136);
                box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
                border: none;
                color: #FFFFFF;
            }

        }

        .form-wrapper {
            .form-item {
                width: 610rpx;
                min-height: 100rpx;
                padding: 20rpx 0 20rpx 20rpx;

                border-bottom: 1rpx solid #EBEBEB;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .text {
                    width: 120rpx;
                    font-size: 30rpx;
                    color: #333333;
                    white-space: nowrap;
                }

                input {
                    width: 400rpx;
                    height: 90rpx;
                    border-radius: 8rpx;
                    padding: 0 30rpx;
                    box-sizing: border-box;
                    font-size: 30rpx;
                    text-align: right;

                    &::placeholder {
                        color: #999;
                    }
                }
            }
        }

        .notice-text {
            display: flex;
            align-items: center;
            color: #D3D2D2;
            font-size: 20rpx;
            text-align: center;
            margin: 30rpx 0;
            justify-content: center;

            .notice-icon {
                width: 28rpx;
                margin-right: 10rpx;
            }
        }

        .submit-btn {
            width: 100%;
            height: 90rpx;
            line-height: 90rpx;
            text-align: center;
            background: linear-gradient(to right, #FF7A45, #FF4B4B);
            color: #fff;
            font-size: 32rpx;
            border-radius: 45rpx;
            margin: 50rpx 0;
        }

        .help-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            text-align: center;
            color: #D3D2D2;
            font-size: 20rpx;

            text {
                margin-bottom: 30rpx;
                margin: 0 20rpx;
            }
        }
    }

}
</style>