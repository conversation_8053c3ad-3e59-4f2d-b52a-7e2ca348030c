<template>
	<tm-app>
		<view class="main">
			<image src="/static/img/loginbg.svg" mode="widthFix" class="waves"></image>
			<view class="logo-area">
				<view class="logo-bg"></view>
				<image src="/static/img/logo.png" mode="widthFix" class="logo"></image>
			</view>
			<view class="nav">
				<view class="nav-item">用户注册</view>
			</view>
			<view class="content">
				<view class="form">
					<input type="number" placeholder="请输入手机号" placeholder-style="color:#909399" v-model="form.phone" />
					<input type="password" placeholder="请输入密码" placeholder-style="color:#909399" v-model="form.password" />
					<view class="yzm_area">
						<input type="text" placeholder="请输入验证码" placeholder-style="color:#909399" v-model="form.yzm" />
						<view class="yzm_button" v-if="!time" @click="sendmsg">获取</view>
						<view class="yzm_button yhq" v-if="time">
							{{time}}s
						</view>
					</view>
				</view>
				<view class="button" @click="submit">注册</view>
				<view class="agree">
					<tm-checkbox-group v-model="checkboxlist" direction="customCol">
						<tm-checkbox :size="28" :fontSize="28" :color="checkboxlist[0]?'primary':'#909399'" value="1"></tm-checkbox>
					</tm-checkbox-group>
					<view class="agree_bottom">
						<text @click="check">
							我已阅读并同意
						</text>
						<uni-link href="https://wx.wansao.com/statics/client/fwxy.html" text="《服务协议》">《服务协议》</uni-link>
						和
						<uni-link href="https://wx.wansao.com/statics/client/yszc.html" text="《隐私政策》">《隐私政策》</uni-link>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import {ref} from 'vue'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmCheckbox from '@/tmui/components/tm-checkbox/tm-checkbox.vue'
	import tmCheckboxGroup from '@/tmui/components/tm-checkbox-group/tm-checkbox-group.vue'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	const form = ref({
		phone:'',
		password:'',
		yzm:''
	})
	const checkboxlist =ref([])
	const check = ()=>{
		checkboxlist.value[0] = checkboxlist.value[0]?'':'1'
	}
	const time = ref(0)
	const sendmsg = ()=>{
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		time.value = 60
		let ct = setInterval(()=>{
			if(time.value>0){
				time.value--
			}else{
				clearInterval(ct)
				ct = null
			}
		},1000)
		api.request.ajax({
			url: '/config/sendmsg',
			type: 'POST',
			whiteList: true,
			data:{
				phone:form.value.phone
			}
		}).then(res => {
			if(res.code===1){
				uni.showToast({ title:'发送成功', icon:'none' })
			}else{
				time.value = 0
				clearInterval(ct)
				ct = null
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const submit = ()=>{
		if(!checkboxlist.value[0])return uni.showToast({
			title:'请先同意《服务协议》和《隐私政策》',
			icon:'none'
		})
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		if(form.value.password.length<6)return uni.showToast({
			title:'密码至少6位',
			icon:'none'
		})
		if(!form.value.yzm)return uni.showToast({
			title:'请填写验证码',
			icon:'none'
		})
		api.request.ajax({
			url: '/login/register',
			type: 'POST',
			whiteList: true,
			data:{
				phone:form.value.phone,
				password:form.value.password,
				yzm:form.value.yzm,
				pid:''
			}
		}).then(res => {
			if(res.code===1){
				uni.setStorageSync('token', res.data.token);
				// #ifdef H5
				window.location.reload()
				// #endif
				// #ifndef H5
				goLink('/pages/index/index')
				// #endif
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
</script>

<style lang="less" scoped>
	@import url(index.less);
	.main{
		.content{
			padding-bottom: 200rpx;
		}
	}
	.agree{
		margin-top: 40rpx;
		display: flex;
		align-items: center;
		color: #909399 !important;
		.agree_bottom{
			display: flex;
			align-items: center;
		}
	}
</style>