<template>
	<tm-app ref="app">
		<view class="main">
			<view class="title">{{ newsDetail.title }}</view>
			<!-- 名片信息 -->
			<view class="maincard" v-if="showUser">
				<image :src="showUser.avatar" mode="widthFix" class="avatar" />
				<view class="center">
					<view class="p1">{{ showUser.nickName }}</view>
				</view>
				<view class="button" @click="handleContact">我要联系</view>
			</view>
			<!-- 使用富文本组件展示内容 -->
			<view class="maincontent">
				<tm-html :content="newsDetail.content" :tag-style="tagStyle" :container-style="htmlStyle"></tm-html>
			</view>
			<!-- 免责声明 -->
			<view class="disclaimer">
				<tm-icon name="tmicon-supervise" color="#FB3838" :font-size="40"></tm-icon>
				<view class="text1">免责<br>声明</view>
				<view class="text2">以上资讯来自互联网转载或整理，不代表和所在单位立场，如有侵权请联系管理员删除！</view>
			</view>
			<!-- ad -->
			<view class="mt-n12">
				<tm-carousel autoplay :round="0" :width="721" :height="197" :indicatorDots="false" imgmodel="widthFix"
					color="#FCD9DB" :list="adInfo" rangKey="src" @click="item=>goLink(item.url||'/pages/news/index?type=1')"></tm-carousel>
			</view>

			<!-- 最新资讯 -->
			<view class="news-list">
				<view class="list-title">{{ title }}</view>
				<view class="content2">
					<view class="container" v-for="item in listData" :key="item.id" @click="goLink(item.link,{title:title})">
						<view class="card">
							<image class="main-image" :src="item.img" mode="aspectFill" v-if="item.img" />
							<view class="left">
								<view class="title">{{item.title}}</view>
								<view class="date" v-if="title==='热点新闻'">{{item.date ||item.time|| '暂无日期'}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部栏 -->
			<view class="tab">
				<button class="button_area" open-type="share" @click="handleCopy">
					<image class="img share-icon" src="/static/img/share.png"></image>
					<view class="text">复制配文并转发</view>
				</button>
				<!-- <button class="button_area button_area2" @click="handleCopy">
					<image class="img" src="/static/img/copy.png"></image>
					<view class="text">复制文案</view>
				</button> -->
				<!-- <tm-icon :font-size="50" name="tmicon-weixin" color="#2BA245"></tm-icon>
				<tm-icon :font-size="50" name="tmicon-pengyouquan" color="#2BA245"></tm-icon> -->
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useTmpiniaStore } from '@/tmui/tool/lib/tmpinia'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import { parseParams } from '@/until/parseParams'
import * as api from '@/api/index.js'
// 生命周期钩子
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 定义接口类型
interface NewsDetail {
	title: string
	content: string
	caption: string
}

interface ShowUser {
	avatar: string
	nickName: string
	logo: string
	path: string
	phone: string
}

interface ShareConfig {
	title: string
	path: string
	imageUrl: string
	timeline?: {
		title: string
		query: string
		imageUrl: string
	}
}

// 响应式数据
const newsDetail = ref<NewsDetail>({
	title: '',
	content: '',
	caption: ''
})

const showUser = ref<ShowUser | null>(null)
const shareConfig = ref<ShareConfig>({
	title: '',
	path: '',
	imageUrl: ''
})
const sid = ref('')
const tagStyle = ref({
	image: 'max-width:100%;text-wrap: normal;white-space: normal;',
	img: 'max-width:100%;text-wrap: normal;white-space: normal;',
	video: 'max-width:100%;text-wrap: normal;white-space: normal;',
	table: 'max-width:100%;text-wrap: normal;white-space: normal;',
	view:'max-width:100%;text-wrap: normal;white-space: normal;',
	text:'max-width:100%;text-wrap: normal;white-space: normal;',
	span:'max-width:100%;text-wrap: normal;white-space: normal;',
	div:'max-width:100%;text-wrap: normal;white-space: normal;',
	p:'max-width:100%;text-wrap: normal;white-space: normal;margin: 10rpx 0',
})
const htmlStyle = ref('font-size: 30rpx;line-height: 60rpx;white-space: normal;')

// 新增响应式数据
const adInfo = ref([])
const listData = ref([])

// 获取新闻详情
const getNewsDetail = async (id: string, sharerId?: string) => {
	try {
		const res = await api.request.ajax({
			url: '/money/getNewsDetail',
			type: 'POST',
			data: {
				id,
				sid: sharerId || ''
			}
		})
		if (res.code === 1) {
			newsDetail.value = res.data.detail
			showUser.value = res.data.showUser
			sid.value = res.data.referId || ''

			// 更新分享配置
			const shareData = {
				...res.data.shareData,
				title: res.data.detail.caption || res.data.shareData.title,
				aid:id,
				column:'news'
			}
			const timelineData = {
				...res.data.shareTimeline,
				title: res.data.detail.caption || res.data.shareTimeline.title
			}

			shareConfig.value = {
				...shareData,
				timeline: timelineData
			}

			setShareApp(shareData)
			setShareTime(timelineData)

			// 添加广告数据
			adInfo.value = res.data.ad || {}
			// 添加列表数据
			listData.value = res.data.listData || []
		}
	} catch (error) {
		console.error('获取新闻详情失败:', error)
	}
}

// 处理联系按钮点击 - 修改为使用path跳转
const handleContact = () => {
	if (showUser.value?.phone) {
		uni.makePhoneCall({
			phoneNumber: showUser.value.phone,
			fail(err) {
				console.error('拨打电话失败:', err)
			}
		})
	}
}

// 处理复制文案功能
const handleCopy = () => {
	const copyText = newsDetail.value.caption || newsDetail.value.title
	uni.setClipboardData({
		data: copyText,
		success: () => {
		},
		fail: () => {
		}
	})
}
const title = ref('')
onLoad((e) => {
	const params = parseParams(e)
	if(params.title){
		title.value = params.title
		uni.setNavigationBarTitle({
			title: params.title
		})
	}else{
		uni.setNavigationBarTitle({
			title: '资讯详情'
		})
	}
	if (params.id) {
		getNewsDetail(params.id, params.sid)
	}
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	overflow-x: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	background-color: #fff;
	padding: 40rpx;
	padding-bottom: 200rpx;

	.title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
		text-align: left;
		margin-bottom: 30rpx;
	}

	.maincontent {
		width: 100%;
		padding: 0 20rpx;
		margin-top: 36rpx;
		margin-bottom: 40rpx;

		:deep(img) {
			max-width: 100% !important;
			height: auto !important;
		}

		:deep(p) {
			margin: 20rpx 0;
			line-height: 1.6;
			color: #333;
			font-size: 28rpx;
		}
	}

	.maincard {
		margin-top: 36rpx;
		width: 686rpx;
		height: 167rpx;
		background: #FCFCFC;
		border-radius: 26rpx;
		border: 1rpx solid #D9D9D9;
		display: flex;
		align-items: center;
		padding: 0 30rpx 0 40rpx;

		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}

		.center {
			margin-left: 30rpx;
			flex: 1;

			.p1 {
				font-size: 30rpx;
				color: #333;
				margin-bottom: 10rpx;
			}

			.p2 {
				font-size: 24rpx;
				color: #9C9C9C;
				margin-bottom: 6rpx;
			}
		}

		.button {
			width: 209rpx;
			height: 87rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236, 84, 64, 0.41);
			border-radius: 43rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #fff;
		}
	}
	.tab{
		width: 750rpx;
		background: #FFFFFF;
		box-shadow: 0px -5rpx 18rpx 0px rgba(181,181,181,0.14);
		position: fixed;
		bottom: 0;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx); /* iOS 11.2以下 */
		padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx); /* iOS 11.2及以上 */
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 40rpx;
		padding-right: 40rpx;
		padding-top: 20rpx;
		.button_area{
			width: 280rpx;
			height: 90rpx;
			border-radius: 10rpx;
			border: 1rpx solid #FB3838;
			display: flex;
			align-items: center;
			justify-content: center;
			.img{
				width: 40rpx;
				height: 40rpx;
			}
			.text{
				margin-left: 16rpx;
				font-size: 26rpx;
				line-height: 50rpx;
				color: #FB3838;
			}
			.share-icon {
				filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2270%) hue-rotate(343deg) brightness(99%) contrast(97%);
			}
		}
		.button_area2{
			border: 1rpx solid #FF8627;
			.text{
				color: #FF8627;
			}
		}
	}
	.disclaimer {
		width: 690rpx;
		min-height: 100rpx;
		border-radius: 20rpx;
		background: #FFF7F7;
		border: 2rpx solid rgba(251, 56, 56, 0.2);
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		margin-top: 30rpx;
		gap: 16rpx;

		.text1 {
			font-size: 28rpx;
			font-weight: 500;
			color: #FB3838;
			padding-right: 16rpx;
			border-right: 2rpx solid rgba(251, 56, 56, 0.3);
			white-space: pre-line;
			line-height: 32rpx;
			text-align: center;
		}

		.text2 {
			flex: 1;
			font-size: 24rpx;
			color: #666;
			line-height: 36rpx;
		}
	}

	.ad {
		width: 690rpx;
		margin: 30rpx 0;
		border-radius: 20rpx;
		overflow: hidden;

		image {
			width: 100%;
			vertical-align: middle;
		}
	}

	.news-list {
		width: 690rpx;
		margin-top: 40rpx;

		.list-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 24rpx;
			position: relative;
			padding-left: 20rpx;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 28rpx;
				background: #FB3838;
				border-radius: 3rpx;
			}
		}
		.card-base() {
		display: flex;
		position: relative;
		
		.left {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			flex: 1;
			
			.title {
				color: #333333;
				line-height: 1.5;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
			}
			
			.date {
				font-size: 24rpx;
			}
		}
		
		.main-image {
			flex-shrink: 0;
			border-radius: 16rpx;
		}
	}
	

	.content2 {
		width: 690rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #FFFFFF;
		box-shadow: 1rpx 1rpx 0px 0px #FFFFFF;
		border-radius: 20rpx 26rpx 26rpx 20rpx;
		padding: 20rpx 0;
		
		.container {
			width: 100%;
			&:last-child {
				.card {
					border-bottom: none;
				}
			}
		}
		
		.card {
			.card-base();
			height: 170rpx;
			border-bottom: 1rpx solid #F3F3F3;
			justify-content: space-between;
			flex-direction: row;
			align-items: center;
			padding: 20rpx 0;
			margin: 0;
			.left{
				margin-left: 20rpx;
                .title {
                    font-size: 30rpx;
                    font-weight: 400;
                    margin: 0;
                }
                
                .date {
                    color: #D3D2D2;
                    
                    &::before {
                        display: none;
                    }
                }
            }

			
			.main-image {
				width: 200rpx;
				height: 120rpx;
				
			}
		}
	}

	}
}
</style>