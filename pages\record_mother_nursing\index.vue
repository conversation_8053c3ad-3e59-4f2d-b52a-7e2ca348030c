<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center overflow mt-16">
				<tm-image :width="688" :height="218" src="/static/img/info_bg3.png"></tm-image>
				<tm-image :width="159" :height="213" src="/static/img/info_people.png" class="absolute r-n10 b--10"></tm-image>
				<view class="absolute flex-col flex-col-center-left" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff"
						label="宝妈护理记录"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff"
						label="BAOMA NURSING RECORDS"></tm-text>
				</view>
			</view>
			<view class="remark_area">
				<view v-for="(item, index) in formItems" :key="index" class="form_item form_item2">
					<view class="form_item_title">
						{{ item.title }}
						<text v-if="item.tip" class="form_item_title_tip">{{ item.tip }}</text>
					</view>
					<view class="form_item_content">
						<!-- 输入框类型 -->
						<template v-if="item.type === 'input'">
							<view v-for="(input, inputIndex) in item.inputs" :key="inputIndex" class="form_item_content_switch">
								<input
									type="text"
									class="input"
									:placeholder="input.placeholder"
									placeholder-style="color: #D3D2D2"
									v-model="input.value"
								>
								<text v-if="input.unit" class="unit">{{ input.unit }}</text>
							</view>
						</template>
						<!-- 选择类型 -->
						<template v-else-if="item.type === 'select'">
							<view
								v-for="(option, optionIndex) in item.options"
								:key="optionIndex"
								class="form_item_content_switch"
								:class="{ active: item.selectedValue === option.value }"
								@click="selectOption(index, option.value)"
							>
								{{ option.label }}
							</view>
						</template>
						<!-- 选择类型 -->
						<template v-else-if="item.type === 'img'">
							<tm-upload 
							v-model="list" 
							:width="list.length?420:210" 
							:rows="list.length?2:1" 
							:imageHeight="200" 
							:url="uploadUrl" 
							formName="photo"
							:maxSize="15*1024*1024"
							:onSuccessAfter="onSuccess"
							:onRemove="e=>onRemove(e,'17')"
							:chooesefileAfter="chooesefileAfter"
							:formData="{
								token,
								type:'17',
								}">
							</tm-upload>
						</template>
					</view>
				</view>
				<view class="submit-btn" @click="submitRecord">提交</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia'
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { useIdCardUpload } from '@/until/useIdCardUpload'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 响应式数据
const store = useStore()
const formItems = ref([
	{
		title: '乳房护理清洁',
		type: 'input',
		inputs: [
			{
				value: '',
				placeholder: '请输入',
				unit: '℃'
			},
			{
				value: '',
				placeholder: '请输入',
				unit: '℃'
			}
		]
	},
	{
		title: '伤口清洁与消毒',
		type: 'select',
		selectedValue: '',
		options: [
			{
				label: '完成',
				value: 'completed'
			},
			{
				label: '无需服务',
				value: 'no_service'
			}
		]
	},
	{
		title: '上传图片',
		tip: '',
		type: 'img',
		selectedValue: '',
	},
])

// 选择选项
const selectOption = (itemIndex, value) => {
	formItems.value[itemIndex].selectedValue = value
}
// 使用 useIdCardUpload
const { list, idObj, chooesefileAfter, onSuccess, onRemove } = useIdCardUpload()
const uploadUrl = api.baseUrl + '/Center/uploadPhotoSave'
const token = computed(() => store.token)
// 提交记录
const submitRecord = async() => {
	// 收集表单数据
	const formData = {}

	formItems.value.forEach((item, index) => {
		if (item.type === 'input') {
			formData[item.title] = item.inputs.map(input => input.value)
		} else if (item.type === 'select') {
			formData[item.title] = item.selectedValue
		}
	})

	console.log('表单数据:', formData)

	// 这里可以添加表单验证逻辑

	const res = await api.request.ajax({
		url: '/addremark',
		type: 'POST',
		data:{
			type: 2, // 婴儿护理记录类型
			value: JSON.stringify(formData)
		}
	});

	if (res.code === 200) {
		uni.showToast({
			title: '提交成功',
			icon: 'success'
		})
	}
}

</script>
<style lang="less">
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.topbox {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 50rpx;
	}

	.bottombox {
		position: absolute;
		z-index: 2;
		bottom: 0%;
		left: 50%;
		transform: translate(-50%, 50%);
	}
}
</style>