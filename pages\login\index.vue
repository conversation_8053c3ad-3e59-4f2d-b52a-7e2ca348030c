<template>
	<tm-app>
		<view class="main">
			<image src="/static/img/loginbg.svg" mode="widthFix" class="waves"></image>
			<view class="logo-area">
				<view class="logo-bg"></view>
				<image src="/static/img/logo.png" mode="widthFix" class="logo"></image>
			</view>
			<view class="nav">
				<view class="nav-item" :class="navpick===1?'navpick':''" @click="navpick=1">账号登录</view>
				<view class="nav-item" :class="navpick===2?'navpick':''" @click="navpick=2">短信登录</view>
				<view class="line" :class="navpick===2?'lineright':''"></view>
			</view>
			<view class="content">
				<view class="form">
					<input type="number" placeholder="请输入手机号" placeholder-style="color:#909399" v-model="form.phone" />
					<input type="password" placeholder="请输入密码" placeholder-style="color:#909399" v-model="form.password" v-if="navpick===1" />
					<view class="yzm_area" v-if="navpick===2">
						<input type="text" placeholder="请输入验证码" placeholder-style="color:#909399" v-model="form.yzm" />
						<view class="yzm_button" v-if="!time" @click="sendmsg">获取</view>
						<view class="yzm_button yhq" v-if="time">
							{{time}}s
						</view>
					</view>
					<view class="other">
						<view class="left" @click="goLink('/pages/login/forgot')">忘记密码</view>
						<view class="right">还没有账号？点击<text @click="goLink('/pages/login/register')">注册</text></view>
					</view>
				</view>
				<view class="button" @click="submit">登录</view>
<!-- 				<view class="tplogin">
					<view class="tptit">
						<view class="line"></view>
						<view class="text">使用第三方登录</view>
						<view class="line"></view>
					</view>
					<view class="icon-area">
						<tm-icon :font-size="42" color="#fff" name="tmicon-weixin"></tm-icon>
					</view>
				</view> -->
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import tmIcon from '@/tmui/components/tm-icon/tm-icon.vue'
	import { onLoad,onUnload } from '@dcloudio/uni-app'
	import { ref } from 'vue'
	import { goLink } from '@/until/index'
	import * as api from '@/api/index.js'
	import { useStore } from '@/until/mainpinia';
	const store = useStore()
	const getIndex = ()=>{
		api.request.ajax({
			url: '/config/index',
			type: 'POST',
			whiteList: true,
		}).then(res => {
			console.log(res);
			if(res.code===1){
				store.$patch((state) => {
					state.servicephone = res.data.phone
					state.setting = res.data
				})
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	onLoad(()=>{
		let token = uni.getStorageSync('token')
		if(token){
			return uni.reLaunch({
				url:'/pages/index/index'
			})
		}
		getIndex()
	})
	const form = ref({
		phone:'',
		password:'',
		yzm:''
	})
	const navpick = ref(1)
	const time = ref(0)
	const sendmsg = ()=>{
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		time.value = 60
		let ct = setInterval(()=>{
			if(time.value>0){
				time.value--
			}else{
				clearInterval(ct)
				ct = null
			}
		},1000)
		api.request.ajax({
			url: '/config/sendmsg',
			type: 'POST',
			whiteList: true,
			data:{
				phone:form.value.phone
			}
		}).then(res => {
			if(res.code===1){
				uni.showToast({ title:'发送成功', icon:'none' })
			}else{
				time.value = 0
				clearInterval(ct)
				ct = null
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}
	const submit = ()=>{
		if(!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(form.value.phone))return uni.showToast({
			title:'请填写正确的手机号',
			icon:'none'
		})
		if(navpick.value===1&&form.value.password.length<6)return uni.showToast({
			title:'密码至少6位',
			icon:'none'
		})
		if(navpick.value===2&&!form.value.yzm)return uni.showToast({
			title:'请填写验证码',
			icon:'none'
		})
		if(navpick.value===1){
			api.request.ajax({
				url: '/login/applogin',
				type: 'POST',
				whiteList: true,
				data:{
					phone:form.value.phone,
					password:form.value.password
				}
			}).then(res => {
				if(res.code===1){
					uni.setStorageSync('token', res.data.token);
					// #ifdef H5
					window.location.reload()
					// #endif
					// #ifndef H5
					goLink('/pages/index/index')
					// #endif
				}else{
					uni.showToast({ title:res.msg, icon:'none' })
				}
			})
		}
		if(navpick.value===2){
			api.request.ajax({
				url: '/login/appyzmlogin',
				type: 'POST',
				whiteList: true,
				data:{
					phone:form.value.phone,
					yzm:form.value.yzm
				}
			}).then(res => {
				if(res.code===1){
					uni.setStorageSync('token', res.data.token);
					goLink('/pages/index/index')
				}else{
					uni.showToast({ title:res.msg, icon:'none' })
				}
			})
		}
	}
</script>

<style lang="less" scoped>
	@import url(index.less);
</style>