<template>
	<tm-app ref="app">
		<view class="main">
			<view class="card">
				<image mode="widthFix" :src="activityDetail.img" class="main_img"></image>
				<view class="card_content">
					<view class="title">{{ activityDetail.title }}</view>
					<view class="time">{{ activityDetail.time }}</view>
					<view class="other">
						{{ activityDetail.address }}\n
						{{ activityDetail.festival }}\n
						{{ activityDetail.otherField }}{{ activityDetail.otherFieldValue }}
					</view>
					<view class="bottom">
						<view class="avatar_area">
							<image class="avatar" v-for="(avatar, index) in activityDetail.person" 
								   :key="index" :src="avatar"></image>
							<view class="text">{{ activityDetail.num }}人围观</view>
						</view>
						<view class="button" v-if="activityDetail.openEnrolment">
							{{ activityDetail.bmTotal }}
						</view>
					</view>
				</view>
			</view>
			<view class="card">
				<tm-html 
					:content="content" 
					:tag-style="tagStyle"
					:container-style="htmlStyle"
				></tm-html>
			</view>
			<view class="tab">
				<button class="button_area" open-type="share">
					<image class="img share-icon" src="/static/img/share.png"></image>
					<view class="text">分享</view>
				</button>
				<button class="button_area" v-if="createPosters" @click="handlePoster">
					<image class="img" src="/static/img/autocreated.png"></image>
					<view class="text">海报生成</view>
				</button>
				<view class="button1" @click="handleCall">一键拔号</view>
				<view class="button2" @click="handleSignup">{{ btnName }}</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import { parseParams } from '@/until/parseParams'
import * as api from '@/api/index.js'
// Share functionality
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// Add type declaration for uni
declare const uni: any

// Define interfaces for API response
interface ActivityInfo {
    id: number
    link: string
    img: string
    title: string
    caption: string
    address: string
    time: string
    festival: string
    num: string
    person: string[]
    otherField: string
    otherFieldValue: string
    openEnrolment: boolean
    bmTotal: string
    bmnumCount: number
    remainNum: number
}

interface ShareConfig {
    title: string
    path: string
    imageUrl: string
}

// Define interface for poster API response
interface PosterResponse {
    code: number
    msg: string
    data: {
        url: string
    }
}

// Add reactive data
const activityDetail = ref<ActivityInfo>({} as ActivityInfo)
const phone = ref('')
const content = ref('')
const btnStatus = ref(0)
const btnName = ref('')
const createPosters = ref(false)
const shareConfig = ref<ShareConfig>({} as ShareConfig)

// Update the style configuration for tm-html
const tagStyle = ref({
	image: 'max-width:100%;text-wrap: normal;white-space: normal;',
	img: 'max-width:100%;text-wrap: normal;white-space: normal;',
	video: 'max-width:100%;text-wrap: normal;white-space: normal;',
	table: 'max-width:100%;text-wrap: normal;white-space: normal;',
	view:'max-width:100%;text-wrap: normal;white-space: normal;',
	text:'max-width:100%;text-wrap: normal;white-space: normal;',
	span:'max-width:100%;text-wrap: normal;white-space: normal;',
	div:'max-width:100%;text-wrap: normal;white-space: normal;',
	p:'max-width:100%;text-wrap: normal;white-space: normal;margin: 10rpx 0',
})

// Change htmlStyle from object to string
const htmlStyle = ref('font-size: 28rpx; line-height: 48rpx; padding: 20rpx;')

// Get activity details
const getActivityDetail = async (id: string, sharerId?: string) => {
    try {
        const res = await api.request.ajax({
            url: '/money/getActDetail',
            type: 'POST',
            data: {
                id,
                sid: sharerId || ''
            }
        })
        if (res.code === 1) {
            activityDetail.value = res.data.activeInfo
            phone.value = res.data.phone
            content.value = res.data.content
            btnStatus.value = res.data.btnStatus
            btnName.value = res.data.btnName
            createPosters.value = res.data.createPosters

            // Update share config
            const shareData = {
				...res.data.shareData,
				aid:id,
				column:'activity'
			}
            const timelineData = res.data.shareTimeline
            setShareApp(shareData)
            setShareTime(timelineData)
        }
    } catch (error) {
        console.error('获取活动详情失败:', error)
    }
}

// Handle page load
onLoad((e) => {
	const params = parseParams(e)
    if (params.id) {
        getActivityDetail(params.id, params.sid)
    }
})

// Update the handlePoster function
const handlePoster = async () => {
    if (createPosters.value) {
        try {
            const res = await api.request.ajax<PosterResponse>({
                url: '/money/createhd',
                type: 'POST',
                data: {
                    id: activityDetail.value.id
                }
            })
            
            if (res.code === 1) {
                // Open the poster URL
                uni.previewImage({
                    urls: [res.data.url],
                    current: 0,
                    success: () => {
                        console.log('海报预览成功')
                    },
                    fail: (err) => {
                        console.error('海报预览失败:', err)
                    }
                })
            } else {
                uni.showToast({
                    title: res.msg || '生成海报失败',
                    icon: 'none'
                })
            }
        } catch (error) {
            console.error('生成海报失败:', error)
            uni.showToast({
                title: '生成海报失败',
                icon: 'none'
            })
        }
    }
}

// Handle signup button click
const handleSignup = () => {
    switch (btnStatus.value) {
        case 1: // 静候开启
            uni.showToast({
                title: '活动报名未开始',
                icon: 'none'
            })
            break
            
        case 2: // 立即报名
            if (activityDetail.value.link) {
                goLink(activityDetail.value.link)
            } else {
                goLink('/pages/signUp/index', {
                    id: activityDetail.value.id
                })
            }
            break
            
        case 3: // 报名结束
            uni.showToast({
                title: '活动报名已结束',
                icon: 'none'
            })
            break
            
        case 4: // 报名成功
            uni.showToast({
                title: '您已报名成功',
                icon: 'none'
            })
            break
            
        default:
            console.warn('未知的按钮状态:', btnStatus.value)
            break
    }
}

// Add handleCall function
const handleCall = () => {
    if (phone.value) {
        uni.makePhoneCall({
            phoneNumber: phone.value,
            fail(err) {
                console.error('拨打电话失败:', err)
            }
        })
    }
}

// 页面数据
const store = useStore()
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1rpx solid #E8E8E8;
	
	.card {
		margin-top: 40rpx;
		width: 686rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		overflow: hidden;
		.main_img{
			width: 100%;
		}
		.card_content{
			display: flex;
			flex-direction: column;
			padding: 45rpx 24rpx 45rpx 30rpx;
			.title{
				font-size: 34rpx;
				font-weight: bold;
				margin-bottom: 20rpx;
				color: #333;
			}
			.time{
				font-size: 28rpx;
				line-height: 48rpx;
				color: #9C9C9C;
			}
			.other{
				font-size: 28rpx;
				line-height: 48rpx;
				color: #9C9C9C;
				word-break: break-all;      
				word-wrap: break-word;      
				white-space: pre-wrap; 
			}
			.bottom{
				margin-top: 35rpx;
				width: 100%;
				padding-top: 35rpx;
				border-top: 1rpx solid #E8E8E8;
				display: flex;
				justify-content: space-between;
				.avatar_area{
					display: flex;
					align-items: center;
					.avatar{
						margin-left: -16rpx;
						width: 38rpx;
						height: 38rpx;
						border-radius: 50%;
						background-color: #fff;
						overflow: hidden;
						&:nth-child(1){
							margin-left: 0;
						}
					}
					.text{
						font-size: 24rpx;
						color: #9C9C9C;
						margin-left: 10rpx;
					}
				}
				.button{
					width: 209rpx;
					height: 87rpx;
					background: linear-gradient(-15deg, #F31630, #FF6136);
					box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
					border-radius: 43rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 30rpx;
					color: #fff;
				}
			}
		}
		.detail{
			width: 100%;
			height: 100%;
			min-height: 400rpx;
			padding: 20rpx;
		}
	}
	.tab{
		width: 750rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -5rpx 18rpx 0rpx rgba(181,181,181,0.14);
		position: fixed;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 30rpx;
		padding-right: 52rpx;
		padding-top: 20rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx); /* iOS 11.2以下 */
		padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx); /* iOS 11.2及以上 */
		.button_area{
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			.img{
				width: 40rpx;
				height: 40rpx;
			}
			.text{
				font-size: 26rpx;
				line-height: 50rpx;
				color: #333;
			}
		}
		.button1{
			width: 198rpx;
			height: 90rpx;
			background: linear-gradient(-31deg, #FFA146, #FF7B1A);
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			color: #fff;
		}
		.button2{
			width: 198rpx;
			height: 90rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			color: #fff;
			margin-left: -40rpx;
		}
	}
}
</style>