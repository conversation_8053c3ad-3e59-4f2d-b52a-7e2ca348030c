<template>
    <tm-app>
        <view class="main">
            <customNavigationBar :label="NavigationBarTitle || '接单宝'" :scrollTop="scrollTop" />
			<view class="swipe">
				<image class="banner-img" src="/static/img/banner4.png" mode="aspectFill"></image>
			</view>
            <!-- <view class="header-bill">
                <image src="https://wx.wansao.com/statics/images/family/person.png" class="person" mode="widthFix" />
                <view class="header-text">
                    <view class="stat-item">
                        <text class="stat-label">已有</text>
                        <text class="big">{{ aunt }}位</text>
                        <text class="stat-label">阿姨完成名片/简历制作</text>
                    </view>
                    <view class="stat-item">
                        <text class="stat-label">已有</text>
                        <text class="big">{{ customer }}位</text>
                        <text class="stat-label">客户查看阿姨名片/简历制作</text>
                    </view>
                    <view class="stat-item">
                        <text class="stat-label">已有</text>
                        <text class="big">{{ contract }}位</text>
                        <text class="stat-label">客户通过阿姨名片/简历成功签约合同</text>
                    </view>
                </view>
            </view> -->
            <view class="resume_cardbg">
				<image src="/static/img/resume_cardbg.png" class="resume_cardbg_mainImg"></image>
				<view class="resume_cardbg_content">
					<view class="p1"><text class="p1_text">接单宝</text></view>
					<view class="p2">已有<text class="p2_text">{{ aunt }}</text>名阿姨入驻<br>已服务<text class="p2_text">{{ customer }}</text>位客户</view>
				</view>
                <view class="resume_cardbg_content" v-if="rightBtn.show">
					<view class="box" @click="goLink(rightBtn.url)">
						<view class="boxcontent">
							<tm-icon name="tmicon-qiandai" :font-size="50" color="#fff"></tm-icon>
							<view class="text1">{{ rightBtn.title }}</view>
						</view>
					</view>
				</view>
			</view>
            <view class="mt-n12">
				<tm-carousel autoplay :round="0" :width="721" :height="197" :indicatorDots="false" imgmodel="widthFix"
					color="#FCD9DB" :list="adList" rangKey="src" @click="item=>goLink(item.url||'/pages/news/index?type=1')"></tm-carousel>
			</view>
            <view class="area2">
                <view v-for="(item, index) in cardList" :key="index">
                    <view class="resume_card" @click="goLink(item.url)" v-if="item.show">
                        <image class="bg-image" :src="item.bgImg" mode="aspectFill"></image>
                        <view class="card_content">
                            <view class="p1">{{ item.title }}</view>
                            <view class="p2">{{ item.desc }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <tabber></tabber>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'
import { goLink } from '@/until/index'
import tabber from '@/components/tabber/tabber.vue'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import { useStore } from '@/until/mainpinia';
const { NavigationBarTitle } = snb()
// 分享功能
const { onShareAppMessage, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()
const scrollTop = ref(0)
onPageScroll((e) => {
    scrollTop.value = e.scrollTop
})
const store = useStore()
const adList = ref([])
const bannerList = computed(() => store.setting.bannerList)
// 响应式数据
const aunt = ref('321')
const customer = ref('56558')
const contract = ref('558')
const isFamily = ref(false)
const musicShow = ref(false)
const shareData = ref({})
const refresh = ref(true)
const cardList = ref([])
const rightBtn = ref({})
// 获取首页信息
const getIndexInfo = async () => {
    try {
        refresh.value = false
        const res = await api.request.ajax({
            url: '/Home/orderHall',
            type: 'POST'
        })
        if (res.code !== 1) {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return false
        }
        refresh.value = true
        shareData.value = res.data.shareData
        musicShow.value = res.data.musicShow
        aunt.value = res.data.aunt
        customer.value = res.data.customer
        contract.value = res.data.contract
        isFamily.value = res.data.isFamily
        cardList.value = res.data.btnList
        adList.value = res.data.adList
        rightBtn.value = res.data.rightBtn
    } catch (error) {
        console.error('获取首页信息失败:', error)
    }
}

// 生命周期钩子
onLoad(() => {
    getIndexInfo()
})

onShow(() => {
    if (refresh.value) {
        getIndexInfo()
    }
})
</script>

<style lang="scss" scoped>
.main {
    width: 750rpx;
	min-height: 100vh;
	overflow-x: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
    padding-bottom: 180rpx;
}

.area2 {
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.resume_cardbg {
		width: 690rpx;
		height: 313rpx;
		position: relative;
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 69rpx 0 56rpx;
		.resume_cardbg_mainImg{
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
		}
		.resume_cardbg_content{
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			position: relative;
			z-index: 2;
			.p1 {
				position: relative;
				.p1_text{
					font-weight: bold;
					font-size: 61rpx;
					color: #333;
					position: relative;
					z-index: 2;
				}
				&::after{
					width: 197rpx;
					height: 14rpx;
					background: rgba(242, 62, 70, 0.45);
					border-radius: 7rpx;
					content: '';
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					bottom: 4rpx;
				}
			}

			.p2 {
				margin-top: 50rpx;
				font-size: 24rpx;
				color: #9C9C9C;
                .p2_text{
                    font-size: 40rpx;
                    color: #FF295C;
                    font-weight: bold;
                    margin: 0 10rpx;
                }
			}
			.resume_cardbg_button {
				width: 262rpx;
				height: 88rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
				border-radius: 44rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 60rpx;
			}
			.resume_cardbg_button2{
				margin-top: 30rpx;
				background: linear-gradient(-31deg, #7681F6, #574DF2);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(118, 129, 246, 0.41);
			}
		}
	}
.header-bill {
    margin: 0 auto 0;
    position: relative;
    z-index: 2;
    width: 690rpx;
    min-height: 300rpx;
    background: linear-gradient(135deg, #FFFFFF, #F8F9FF);
    box-shadow: 0 15rpx 35rpx rgba(149, 157, 165, 0.15);
    border-radius: 35rpx;
    padding: 45rpx 35rpx;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    backdrop-filter: blur(10px);

    &:hover {
        transform: translateY(-5rpx);
        box-shadow: 0 20rpx 45rpx rgba(149, 157, 165, 0.2);
    }
}

.header-text {
    margin-left: 25rpx;
    flex: 1;

    .stat-item {
        margin-bottom: 28rpx;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 8rpx 0;
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.02);
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .stat-label {
        font-size: 28rpx;
        color: rgba(102, 102, 102, 0.85);
        line-height: 1.6;
        letter-spacing: 0.5rpx;
    }
}

.big {
    font-size: 40rpx;
    font-weight: 700;
    background: linear-gradient(45deg, #FF295C, #FF5B4C);
    -webkit-background-clip: text;
    color: transparent;
    margin: 0 10rpx;
    text-shadow: 0 2rpx 4rpx rgba(255, 41, 92, 0.1);
}

.person {
    margin-top: 20rpx;
    width: 160rpx;
    filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: translateY(-5rpx) scale(1.08);
        filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.15));
    }
}
</style>