<template>
    <tm-app>
        <view class="container">
            <view class="tj_top" :style="{ backgroundImage: `url('https://wx.wansao.com/statics/family/sendhall/tj_top_bg.png')` }">
                <image src="/static/img/tj_top_tit.png" mode="" class="tj_top_tit"/>
                <image src="/static/img/tj_top_hb.png" mode="" class="tj_top_hb"/>
            </view>

            <view class="tj-info" :style="{ backgroundImage: `url('https://wx.wansao.com/statics/family/sendhall/tj_info.png')` }">
                <view class="tj-info-item">
                    <label>被推荐人姓名：</label>
                    <input type="text" placeholder="请填写" v-model="username"/>
                </view>

                <view class="tj-info-item">
                    <label>被推荐人电话：</label>
                    <input type="number" placeholder="请填写" v-model="phone"/>
                </view>

                <image src="/static/img/tj_btn.png" mode="" class="tj_btn" @click="submit"/>
            </view>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'

const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

// 响应式数据
const username = ref('')
const phone = ref('')

// 提交表单
const submit = async () => {
    if (!username.value) {
        uni.showToast({
            title: '请输入被推荐人姓名',
            icon: 'none'
        })
        return false
    }
    if (!(/^1[3456789]\d{9}$/.test(phone.value))) {
        uni.showToast({
            title: '请输入正确的手机号',
            icon: 'none'
        })
        return false
    }

    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/doRecommend',
            type: 'POST',
            data: {
                uname: username.value,
                phone: phone.value
            }
        })
        
        if (res.code === 200) {
            uni.showModal({
                title: '',
                content: res.msg,
                showCancel: false,
                success(res) {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '../ysqiangdan/index'
                        })
                    }
                }
            })
        }
    } catch (error) {
        console.error('提交失败:', error)
    }
}
onShow(() => {
    setTimeout(() => {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#F54021'
        })
    }, 100);
})

</script>

<style lang="scss" scoped>
.container {
    width: 750rpx;
    display: flex;
    align-items: center;
    flex-direction: column;
    background: #ec351a;
}

.tj_top {
    width: 750rpx;
    height: 820rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    &_tit {
        width: 533rpx;
        height: 136rpx;
        margin-top: 190rpx;
    }

    &_hb {
        width: 750rpx;
        height: 483rpx;
    }
}

.tj-info {
    width: 680rpx;
    height: 620rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0rpx 0 60rpx;
    justify-content: center;

    &-item {
        display: flex;
        align-items: center;
        width: 600rpx;
        margin-bottom: 60rpx;

        label {
            color: #9d5b43;
            font-size: 34rpx;
        }

        input {
            width: 360rpx;
            height: 80rpx;
            line-height: 80rpx;
            background: #fff;
            padding: 0 10rpx;
            box-sizing: border-box;
            text-align: center;
            font-size: 32rpx;
            color: #666;
            position: relative;
        }
    }
}

.tj_btn {
    width: 401rpx;
    height: 104rpx;
}
</style>
